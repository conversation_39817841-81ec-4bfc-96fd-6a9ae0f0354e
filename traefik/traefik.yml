global:
  sendAnonymousUsage: false

api:
  dashboard: true
  insecure: true

entryPoints:
  web:
    address: ":80"
    http:
      redirections:
        entrypoint:
          to: websecure
          scheme: https
  websecure:
    address: ":443"

providers:
  docker:
    endpoint: "unix:///var/run/docker.sock"
    exposedByDefault: false
    watch: true

tls:
  certificates:
    - certFile: /etc/letsencrypt/live/eko-api2.nextai.asia/fullchain.pem
      keyFile: /etc/letsencrypt/live/eko-api2.nextai.asia/privkey.pem

log:
  level: INFO

ping: {}
